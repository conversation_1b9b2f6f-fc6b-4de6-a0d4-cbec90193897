# 扫码性能优化专项方案

## 性能目标
- **响应时间**: < 50ms（用户无感知延迟）
- **并发能力**: 支持100+扫码枪同时操作
- **可用性**: 99.9%以上

## 技术架构优化

### 1. 数据库层优化

#### 索引策略
```sql
-- 商品条码索引（最关键）
CREATE INDEX CONCURRENTLY idx_product_barcode ON products(barcode);
CREATE INDEX CONCURRENTLY idx_product_barcode_hash ON products USING hash(barcode);

-- 库存查询索引
CREATE INDEX CONCURRENTLY idx_inventory_product_warehouse ON inventory(product_id, warehouse_id);

-- 操作记录索引
CREATE INDEX CONCURRENTLY idx_operation_log_time ON operation_logs(created_at DESC);
```

#### 数据分区
```sql
-- 按时间分区操作日志表
CREATE TABLE operation_logs (
    id BIGSERIAL,
    barcode VARCHAR(50),
    operation_type VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW()
) PARTITION BY RANGE (created_at);

-- 创建月度分区
CREATE TABLE operation_logs_202401 PARTITION OF operation_logs
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### 2. 缓存策略

#### 多层缓存架构
```python
# L1: 本地缓存（最快）
from cachetools import TTLCache
local_cache = TTLCache(maxsize=10000, ttl=60)

# L2: Redis缓存（中等速度）
import redis
redis_client = redis.Redis(host='localhost', port=6379, db=0)

# L3: 数据库（最慢）
```

#### 缓存预热策略
```python
# 系统启动时预热热点数据
async def warm_up_cache():
    # 预热常用商品信息
    hot_products = await get_hot_products(limit=1000)
    for product in hot_products:
        await cache_product_info(product.barcode, product)
    
    # 预热库存信息
    for warehouse in warehouses:
        inventory = await get_warehouse_inventory(warehouse.id)
        await cache_inventory_info(warehouse.id, inventory)
```

### 3. API设计优化

#### 专用扫码API
```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

class ScanRequest(BaseModel):
    barcode: str
    warehouse_id: int
    operation_type: str  # 'in', 'out', 'check'

@app.post("/api/v1/scan", response_model=ScanResponse)
async def scan_barcode(request: ScanRequest):
    # 1. 参数验证（< 1ms）
    if not request.barcode:
        raise HTTPException(status_code=400, detail="条码不能为空")
    
    # 2. 缓存查询（< 5ms）
    product = await get_product_from_cache(request.barcode)
    if not product:
        # 3. 数据库查询（< 20ms）
        product = await get_product_from_db(request.barcode)
        if product:
            await cache_product_info(request.barcode, product)
    
    # 4. 库存查询（< 10ms）
    inventory = await get_inventory_info(product.id, request.warehouse_id)
    
    # 5. 返回结果（< 5ms）
    return ScanResponse(
        product=product,
        inventory=inventory,
        timestamp=datetime.now()
    )
```

#### 批量操作API
```python
@app.post("/api/v1/scan/batch")
async def batch_scan(requests: List[ScanRequest]):
    # 批量处理，减少网络往返
    tasks = [scan_barcode(req) for req in requests]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results
```

### 4. 网络优化

#### HTTP/2 支持
```python
# 使用uvicorn支持HTTP/2
import uvicorn

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        http="h2",  # 启用HTTP/2
        ssl_keyfile="key.pem",
        ssl_certfile="cert.pem"
    )
```

#### 连接池优化
```python
import asyncpg
import asyncio

# 数据库连接池
async def create_db_pool():
    return await asyncpg.create_pool(
        "postgresql://user:pass@localhost/db",
        min_size=10,
        max_size=50,
        command_timeout=5
    )

# Redis连接池
import aioredis
redis_pool = aioredis.ConnectionPool.from_url(
    "redis://localhost:6379",
    max_connections=20
)
```

### 5. 硬件优化建议

#### 服务器配置
```yaml
# 推荐配置
CPU: 16核心 3.0GHz+
内存: 64GB DDR4
存储: NVMe SSD 1TB+
网络: 万兆网卡

# 数据库服务器
CPU: 32核心 3.2GHz+
内存: 128GB DDR4
存储: NVMe SSD RAID10
```

#### 网络架构
```
扫码枪 -> 无线AP -> 交换机 -> 服务器
         (WiFi 6)   (千兆)   (万兆)

# 网络优化
- 使用WiFi 6标准
- 部署多个AP避免信号死角
- 使用专用VLAN隔离扫码流量
```

## 监控和告警

### 性能监控指标
```python
import time
from prometheus_client import Counter, Histogram

# 扫码请求计数器
scan_requests_total = Counter('scan_requests_total', 'Total scan requests')

# 响应时间直方图
scan_response_time = Histogram('scan_response_time_seconds', 'Scan response time')

@app.middleware("http")
async def monitor_performance(request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    if request.url.path.startswith("/api/v1/scan"):
        scan_requests_total.inc()
        scan_response_time.observe(process_time)
    
    return response
```

### 告警规则
```yaml
# Prometheus告警规则
groups:
- name: scan_performance
  rules:
  - alert: ScanResponseTimeTooHigh
    expr: histogram_quantile(0.95, scan_response_time_seconds) > 0.1
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "扫码响应时间过长"
      description: "95%的扫码请求响应时间超过100ms"

  - alert: ScanErrorRateTooHigh
    expr: rate(scan_requests_total{status="error"}[5m]) > 0.01
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "扫码错误率过高"
      description: "扫码错误率超过1%"
```

## 压力测试方案

### 测试工具
```python
# 使用locust进行压力测试
from locust import HttpUser, task, between

class ScanUser(HttpUser):
    wait_time = between(0.1, 0.5)  # 模拟扫码间隔
    
    @task
    def scan_barcode(self):
        barcode = f"TEST{random.randint(1000, 9999)}"
        self.client.post("/api/v1/scan", json={
            "barcode": barcode,
            "warehouse_id": 1,
            "operation_type": "check"
        })

# 运行测试
# locust -f scan_test.py --host=http://localhost:8000
```

### 测试场景
1. **单用户连续扫码**: 模拟单个用户快速连续扫码
2. **多用户并发扫码**: 模拟100个用户同时扫码
3. **高峰期压力测试**: 模拟业务高峰期的并发量
4. **异常场景测试**: 模拟网络延迟、数据库慢查询等异常情况

## 预期性能指标

| 场景 | 响应时间 | 并发用户 | TPS |
|------|----------|----------|-----|
| 正常扫码 | < 30ms | 50 | 1000+ |
| 高峰期 | < 50ms | 100 | 800+ |
| 异常恢复 | < 100ms | 20 | 200+ |

通过以上优化方案，可以确保扫码操作达到毫秒级响应，满足仓库作业的实时性要求。
