# 五金行业管理系统 - 容器化部署方案

## 容器化架构设计

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                        负载均衡层                            │
│                    Nginx + Keepalived                      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        应用服务层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Web前端容器  │  │ API网关容器  │  │ 移动端API   │        │
│  │   Nginx     │  │   Kong      │  │   容器      │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        业务服务层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ 用户服务     │  │ 库存服务     │  │ 订单服务     │        │
│  │ user-svc    │  │inventory-svc│  │ order-svc   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ 报表服务     │  │ 审批服务     │  │ 通知服务     │        │
│  │ report-svc  │  │workflow-svc │  │ notify-svc  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        数据存储层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ PostgreSQL  │  │    Redis    │  │   MinIO     │        │
│  │   主从集群   │  │   集群      │  │  对象存储    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## Docker容器配置

### 1. 基础镜像标准化

#### Python服务基础镜像
```dockerfile
# Dockerfile.python-base
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 创建非root用户
RUN useradd -m -u 1000 appuser
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1
```

#### 前端服务镜像
```dockerfile
# Dockerfile.frontend
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

### 2. 服务容器配置

#### 业务服务容器
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 用户服务
  user-service:
    build:
      context: ./services/user
      dockerfile: Dockerfile
    container_name: user-service
    restart: unless-stopped
    environment:
      - DATABASE_URL=************************************/wujin_db
      - REDIS_URL=redis://redis:6379/0
    ports:
      - "8001:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - wujin-network
    volumes:
      - ./logs/user:/app/logs
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # 库存服务
  inventory-service:
    build:
      context: ./services/inventory
      dockerfile: Dockerfile
    container_name: inventory-service
    restart: unless-stopped
    environment:
      - DATABASE_URL=************************************/wujin_db
      - REDIS_URL=redis://redis:6379/1
    ports:
      - "8002:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - wujin-network
    volumes:
      - ./logs/inventory:/app/logs
```

### 3. 数据库容器配置

#### PostgreSQL主从配置
```yaml
  # 主数据库
  postgres-master:
    image: postgres:15
    container_name: postgres-master
    restart: unless-stopped
    environment:
      - POSTGRES_DB=wujin_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=your_password
      - POSTGRES_REPLICATION_USER=replicator
      - POSTGRES_REPLICATION_PASSWORD=repl_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_master_data:/var/lib/postgresql/data
      - ./config/postgres/master.conf:/etc/postgresql/postgresql.conf
      - ./config/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf
    networks:
      - wujin-network

  # 从数据库
  postgres-slave:
    image: postgres:15
    container_name: postgres-slave
    restart: unless-stopped
    environment:
      - POSTGRES_DB=wujin_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=your_password
      - PGUSER=postgres
      - POSTGRES_MASTER_SERVICE=postgres-master
    ports:
      - "5433:5432"
    volumes:
      - postgres_slave_data:/var/lib/postgresql/data
    depends_on:
      - postgres-master
    networks:
      - wujin-network
```

#### Redis集群配置
```yaml
  # Redis主节点
  redis-master:
    image: redis:7-alpine
    container_name: redis-master
    restart: unless-stopped
    command: redis-server --appendonly yes --replica-read-only no
    ports:
      - "6379:6379"
    volumes:
      - redis_master_data:/data
    networks:
      - wujin-network

  # Redis从节点
  redis-slave:
    image: redis:7-alpine
    container_name: redis-slave
    restart: unless-stopped
    command: redis-server --slaveof redis-master 6379 --appendonly yes
    ports:
      - "6380:6379"
    volumes:
      - redis_slave_data:/data
    depends_on:
      - redis-master
    networks:
      - wujin-network
```

## 容器编排和管理

### 1. Docker Compose配置

#### 开发环境
```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  # 开发环境特殊配置
  user-service:
    build:
      context: ./services/user
      dockerfile: Dockerfile.dev
    volumes:
      - ./services/user:/app  # 代码热重载
    environment:
      - DEBUG=true
      - LOG_LEVEL=debug
```

#### 生产环境
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  # 生产环境配置
  user-service:
    image: wujin/user-service:latest
    restart: always
    environment:
      - DEBUG=false
      - LOG_LEVEL=info
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### 2. 容器监控

#### 健康检查配置
```yaml
services:
  user-service:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

#### 监控服务
```yaml
  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - wujin-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - wujin-network
```

## 部署和运维

### 1. 自动化部署脚本

#### 部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

echo "开始部署五金管理系统..."

# 1. 拉取最新代码
git pull origin main

# 2. 构建镜像
echo "构建Docker镜像..."
docker-compose -f docker-compose.prod.yml build

# 3. 停止旧服务
echo "停止旧服务..."
docker-compose -f docker-compose.prod.yml down

# 4. 启动新服务
echo "启动新服务..."
docker-compose -f docker-compose.prod.yml up -d

# 5. 健康检查
echo "等待服务启动..."
sleep 30

# 检查服务状态
docker-compose -f docker-compose.prod.yml ps

echo "部署完成！"
```

#### 备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# 备份数据库
docker exec postgres-master pg_dump -U postgres wujin_db > $BACKUP_DIR/database.sql

# 备份Redis数据
docker exec redis-master redis-cli BGSAVE
docker cp redis-master:/data/dump.rdb $BACKUP_DIR/

# 备份文件存储
docker run --rm -v minio_data:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/minio_data.tar.gz -C /data .

echo "备份完成: $BACKUP_DIR"
```

### 2. 日志管理

#### 日志收集配置
```yaml
  # ELK日志收集
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    container_name: logstash
    volumes:
      - ./config/logstash/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    container_name: kibana
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

### 3. 安全配置

#### 网络隔离
```yaml
networks:
  wujin-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  
  database-network:
    driver: bridge
    internal: true  # 内部网络，不能访问外网
```

#### 密钥管理
```yaml
secrets:
  db_password:
    file: ./secrets/db_password.txt
  redis_password:
    file: ./secrets/redis_password.txt

services:
  postgres-master:
    secrets:
      - db_password
    environment:
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
```

## 容器化优势

### 1. 开发效率提升
- **环境一致性**: 开发、测试、生产环境完全一致
- **快速部署**: 一键部署所有服务
- **版本管理**: 镜像版本化管理，支持快速回滚
- **资源隔离**: 服务间完全隔离，互不影响

### 2. 运维简化
- **自动扩缩容**: 根据负载自动调整容器数量
- **健康监控**: 自动检测服务健康状态
- **日志集中**: 统一收集和分析日志
- **备份恢复**: 简化备份和恢复流程

### 3. 成本优化
- **资源利用**: 提高服务器资源利用率
- **弹性伸缩**: 按需分配资源
- **维护成本**: 降低系统维护成本

这个容器化方案提供了完整的部署和运维解决方案，确保系统的稳定性和可维护性。
